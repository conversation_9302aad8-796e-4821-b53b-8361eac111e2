<template>
	<view class="user-container">
		<!-- 用户信息头部 -->
		<view class="user-header" v-if="getLoginStatus">
			<u-avatar :src="getUserInfo.avatar || '/static/images/default-avatar.png'" size="80"></u-avatar>
			<view class="user-info">
				<text class="username">{{ getUserInfo.nickname || '用户' }}</text>
				<text class="user-desc">{{ getUserInfo.phone || '未绑定手机号' }}</text>
			</view>
			<u-icon name="arrow-right" color="#fff" size="20"></u-icon>
		</view>
		
		<!-- 未登录状态 -->
		<view class="login-prompt" v-else>
			<u-avatar src="/static/images/default-avatar.png" size="80"></u-avatar>
			<text class="prompt-text">点击登录</text>
			<u-button type="primary" size="medium" @click="goLogin">立即登录</u-button>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-list" v-if="getLoginStatus">
			<u-cell-group>
				<u-cell 
					v-for="menu in menuList" 
					:key="menu.id"
					:title="menu.title"
					:label="menu.desc"
					:icon="menu.icon"
					rightIcon="arrow-right"
					@click="handleMenu(menu)"
				></u-cell>
			</u-cell-group>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section" v-if="getLoginStatus">
			<u-button type="error" @click="logout">退出登录</u-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			menuList: [
				{
					id: 1,
					title: '个人资料',
					desc: '修改个人信息',
					icon: 'account',
					action: 'profile'
				},
				{
					id: 2,
					title: '账号安全',
					desc: '密码、手机号设置',
					icon: 'lock',
					action: 'security'
				},
				{
					id: 3,
					title: '消息通知',
					desc: '推送消息设置',
					icon: 'bell',
					action: 'notification'
				},
				{
					id: 4,
					title: '隐私设置',
					desc: '隐私权限管理',
					icon: 'eye-off',
					action: 'privacy'
				},
				{
					id: 5,
					title: '帮助中心',
					desc: '常见问题解答',
					icon: 'question-circle',
					action: 'help'
				},
				{
					id: 6,
					title: '关于我们',
					desc: '版本信息',
					icon: 'info-circle',
					action: 'about'
				}
			]
		}
	},
	
	methods: {
		// 处理菜单点击
		handleMenu(menu) {
			switch (menu.action) {
				case 'profile':
					uni.navigateTo({
						url: '/pages/profile/profile'
					})
					break
				case 'security':
					uni.navigateTo({
						url: '/pages/security/security'
					})
					break
				default:
					uni.showToast({
						title: '功能开发中',
						icon: 'none'
					})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.user-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.user-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	color: #fff;
	
	.user-info {
		margin-left: 30rpx;
		flex: 1;
		
		.username {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
		}
		
		.user-desc {
			font-size: 28rpx;
			opacity: 0.8;
		}
	}
}

.login-prompt {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 80rpx 40rpx;
	text-align: center;
	color: #fff;
	
	.prompt-text {
		display: block;
		font-size: 32rpx;
		margin: 30rpx 0 40rpx;
	}
}

.menu-list {
	margin: 40rpx 30rpx;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.logout-section {
	padding: 40rpx 30rpx;
}
</style>
