/* 全局样式 */

/* 重置样式 */
* {
	box-sizing: border-box;
}

page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用容器 */
.container {
	padding: 20rpx;
}

.page-content {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

/* 文字样式 */
.text-primary {
	color: #3c9cff;
}

.text-success {
	color: #5ac725;
}

.text-warning {
	color: #ffb800;
}

.text-error {
	color: #f56c6c;
}

.text-info {
	color: #909399;
}

.text-muted {
	color: #999;
}

.text-white {
	color: #fff;
}

.text-black {
	color: #333;
}

/* 字体大小 */
.text-xs {
	font-size: 20rpx;
}

.text-sm {
	font-size: 24rpx;
}

.text-base {
	font-size: 28rpx;
}

.text-lg {
	font-size: 32rpx;
}

.text-xl {
	font-size: 36rpx;
}

.text-2xl {
	font-size: 40rpx;
}

/* 字体粗细 */
.font-light {
	font-weight: 300;
}

.font-normal {
	font-weight: 400;
}

.font-medium {
	font-weight: 500;
}

.font-bold {
	font-weight: 700;
}

/* 文本对齐 */
.text-left {
	text-align: left;
}

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

/* 布局 */
.flex {
	display: flex;
}

.flex-col {
	flex-direction: column;
}

.flex-row {
	flex-direction: row;
}

.flex-wrap {
	flex-wrap: wrap;
}

.flex-1 {
	flex: 1;
}

.justify-start {
	justify-content: flex-start;
}

.justify-center {
	justify-content: center;
}

.justify-end {
	justify-content: flex-end;
}

.justify-between {
	justify-content: space-between;
}

.justify-around {
	justify-content: space-around;
}

.items-start {
	align-items: flex-start;
}

.items-center {
	align-items: center;
}

.items-end {
	align-items: flex-end;
}

.items-stretch {
	align-items: stretch;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }
.ml-4 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }
.mr-4 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-4 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-4 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }
.pl-4 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }
.pr-4 { padding-right: 40rpx; }

/* 宽高 */
.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

/* 圆角 */
.rounded {
	border-radius: 8rpx;
}

.rounded-lg {
	border-radius: 16rpx;
}

.rounded-full {
	border-radius: 50%;
}

/* 阴影 */
.shadow {
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 边框 */
.border {
	border: 1rpx solid #e5e5e5;
}

.border-t {
	border-top: 1rpx solid #e5e5e5;
}

.border-b {
	border-bottom: 1rpx solid #e5e5e5;
}

.border-l {
	border-left: 1rpx solid #e5e5e5;
}

.border-r {
	border-right: 1rpx solid #e5e5e5;
}

/* 背景色 */
.bg-white {
	background-color: #fff;
}

.bg-gray {
	background-color: #f5f5f5;
}

.bg-primary {
	background-color: #3c9cff;
}

.bg-success {
	background-color: #5ac725;
}

.bg-warning {
	background-color: #ffb800;
}

.bg-error {
	background-color: #f56c6c;
}

/* 定位 */
.relative {
	position: relative;
}

.absolute {
	position: absolute;
}

.fixed {
	position: fixed;
}

/* 显示隐藏 */
.hidden {
	display: none;
}

.block {
	display: block;
}

.inline {
	display: inline;
}

.inline-block {
	display: inline-block;
}
