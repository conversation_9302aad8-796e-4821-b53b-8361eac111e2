/**
 * 全局混入
 * 提供通用的方法和计算属性
 */

import { mapGetters } from 'vuex'

export default {
	computed: {
		...mapGetters('user', ['getUserInfo', 'getToken', 'getLoginStatus'])
	},
	
	methods: {
		/**
		 * 检查登录状态
		 */
		checkLogin() {
			if (!this.getLoginStatus) {
				uni.showModal({
					title: '提示',
					content: '请先登录',
					confirmText: '去登录',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}
					}
				})
				return false
			}
			return true
		},
		
		/**
		 * 跳转到登录页
		 */
		goLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		},
		
		/**
		 * 退出登录
		 */
		logout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.$store.dispatch('user/logout')
					}
				}
			})
		},
		
		/**
		 * 格式化时间
		 */
		formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
			return this.$utils.formatDate(time, format)
		},
		
		/**
		 * 相对时间
		 */
		timeAgo(time) {
			return this.$utils.timeAgo(time)
		},
		
		/**
		 * 显示提示
		 */
		showToast(title, icon = 'none') {
			this.$utils.showToast(title, icon)
		},
		
		/**
		 * 显示确认对话框
		 */
		showConfirm(content, title = '提示') {
			return this.$utils.showConfirm(content, title)
		},
		
		/**
		 * 复制文本
		 */
		copyText(text) {
			return this.$utils.copyToClipboard(text)
		},
		
		/**
		 * 预览图片
		 */
		previewImage(urls, current = 0) {
			if (typeof urls === 'string') {
				urls = [urls]
			}
			
			uni.previewImage({
				urls,
				current: typeof current === 'number' ? current : urls.indexOf(current)
			})
		},
		
		/**
		 * 拨打电话
		 */
		makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber
			})
		},
		
		/**
		 * 获取位置信息
		 */
		getLocation() {
			return new Promise((resolve, reject) => {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						resolve(res)
					},
					fail: (error) => {
						console.error('获取位置失败:', error)
						this.showToast('获取位置失败')
						reject(error)
					}
				})
			})
		},
		
		/**
		 * 选择图片
		 */
		chooseImage(options = {}) {
			const defaultOptions = {
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera']
			}
			
			return new Promise((resolve, reject) => {
				uni.chooseImage({
					...defaultOptions,
					...options,
					success: (res) => {
						resolve(res)
					},
					fail: (error) => {
						console.error('选择图片失败:', error)
						reject(error)
					}
				})
			})
		},
		
		/**
		 * 上传图片
		 */
		async uploadImage(filePath, options = {}) {
			try {
				const result = await this.$http.upload('/api/upload/image', filePath, {
					name: 'image',
					...options
				})
				return result.data.url
			} catch (error) {
				console.error('上传图片失败:', error)
				this.showToast('上传失败')
				throw error
			}
		},
		
		/**
		 * 分享
		 */
		share(options = {}) {
			const defaultOptions = {
				title: '分享标题',
				path: '/pages/index/index'
			}
			
			return {
				...defaultOptions,
				...options
			}
		},
		
		/**
		 * 设置页面标题
		 */
		setTitle(title) {
			uni.setNavigationBarTitle({
				title
			})
		},
		
		/**
		 * 页面跳转
		 */
		navigateTo(url, params = {}) {
			// 处理参数
			if (Object.keys(params).length > 0) {
				const query = Object.keys(params)
					.map(key => `${key}=${encodeURIComponent(params[key])}`)
					.join('&')
				url += (url.includes('?') ? '&' : '?') + query
			}
			
			uni.navigateTo({ url })
		},
		
		/**
		 * 页面重定向
		 */
		redirectTo(url, params = {}) {
			// 处理参数
			if (Object.keys(params).length > 0) {
				const query = Object.keys(params)
					.map(key => `${key}=${encodeURIComponent(params[key])}`)
					.join('&')
				url += (url.includes('?') ? '&' : '?') + query
			}
			
			uni.redirectTo({ url })
		},
		
		/**
		 * 返回上一页
		 */
		goBack(delta = 1) {
			uni.navigateBack({ delta })
		},
		
		/**
		 * 重新启动到指定页面
		 */
		reLaunch(url) {
			uni.reLaunch({ url })
		},
		
		/**
		 * 切换到tabBar页面
		 */
		switchTab(url) {
			uni.switchTab({ url })
		}
	},
	
	// 页面生命周期
	onLoad(options) {
		// 保存页面参数
		this.pageOptions = options || {}
	},
	
	onShow() {
		// 页面显示时检查登录状态
		this.$store.dispatch('user/checkLoginStatus')
	}
}
