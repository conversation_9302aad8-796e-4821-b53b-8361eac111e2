<template>
	<view class="register-container">
		<view class="register-header">
			<text class="title">注册账号</text>
			<text class="subtitle">创建您的新账号</text>
		</view>
		
		<view class="register-form">
			<u-form :model="form" ref="registerForm" :rules="rules">
				<u-form-item prop="username" borderBottom>
					<u-input 
						v-model="form.username" 
						placeholder="请输入用户名" 
						prefixIcon="account"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="phone" borderBottom>
					<u-input 
						v-model="form.phone" 
						placeholder="请输入手机号" 
						prefixIcon="phone"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="code" borderBottom>
					<view class="code-input">
						<u-input 
							v-model="form.code" 
							placeholder="请输入验证码" 
							prefixIcon="checkmark"
							clearable
						/>
						<u-button 
							:disabled="codeDisabled" 
							@click="sendCode"
							size="mini"
							type="primary"
							class="code-btn"
						>
							{{ codeText }}
						</u-button>
					</view>
				</u-form-item>
				
				<u-form-item prop="password" borderBottom>
					<u-input 
						v-model="form.password" 
						type="password" 
						placeholder="请输入密码" 
						prefixIcon="lock"
						:passwordIcon="true"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="confirmPassword" borderBottom>
					<u-input 
						v-model="form.confirmPassword" 
						type="password" 
						placeholder="请确认密码" 
						prefixIcon="lock"
						:passwordIcon="true"
						clearable
					/>
				</u-form-item>
				
				<view class="agreement">
					<u-checkbox v-model="agreeTerms">
						我已阅读并同意
					</u-checkbox>
					<text class="link-text" @click="showTerms">《用户协议》</text>
					<text>和</text>
					<text class="link-text" @click="showPrivacy">《隐私政策》</text>
				</view>
				
				<u-button 
					type="primary" 
					:loading="registerLoading"
					@click="handleRegister"
					class="register-btn"
				>
					注册
				</u-button>
			</u-form>
		</view>
		
		<view class="login-link">
			<text>已有账号？</text>
			<text class="link-text" @click="goLogin">立即登录</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			registerLoading: false,
			agreeTerms: false,
			codeDisabled: false,
			codeText: '获取验证码',
			countdown: 60,
			form: {
				username: '',
				phone: '',
				code: '',
				password: '',
				confirmPassword: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名',
						trigger: 'blur'
					},
					{
						min: 3,
						max: 20,
						message: '用户名长度为3-20位',
						trigger: 'blur'
					}
				],
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: 'blur'
					},
					{
						pattern: /^1[3-9]\d{9}$/,
						message: '请输入正确的手机号',
						trigger: 'blur'
					}
				],
				code: [
					{
						required: true,
						message: '请输入验证码',
						trigger: 'blur'
					},
					{
						len: 6,
						message: '验证码为6位数字',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						max: 20,
						message: '密码长度为6-20位',
						trigger: 'blur'
					}
				],
				confirmPassword: [
					{
						required: true,
						message: '请确认密码',
						trigger: 'blur'
					},
					{
						validator: (rule, value, callback) => {
							if (value !== this.form.password) {
								callback(new Error('两次输入的密码不一致'))
							} else {
								callback()
							}
						},
						trigger: 'blur'
					}
				]
			}
		}
	},
	
	methods: {
		// 发送验证码
		async sendCode() {
			if (!this.form.phone) {
				this.$u.toast('请先输入手机号')
				return
			}
			
			if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
				this.$u.toast('请输入正确的手机号')
				return
			}
			
			try {
				const result = await this.$http.post('/api/send-code', {
					phone: this.form.phone,
					type: 'register'
				})
				
				if (result.code === 200) {
					this.$u.toast('验证码已发送')
					this.startCountdown()
				} else {
					this.$u.toast(result.message || '发送失败')
				}
			} catch (error) {
				console.error('发送验证码失败:', error)
				this.$u.toast('发送失败，请重试')
			}
		},
		
		// 开始倒计时
		startCountdown() {
			this.codeDisabled = true
			this.countdown = 60
			
			const timer = setInterval(() => {
				this.countdown--
				this.codeText = `${this.countdown}s后重发`
				
				if (this.countdown <= 0) {
					clearInterval(timer)
					this.codeDisabled = false
					this.codeText = '获取验证码'
				}
			}, 1000)
		},
		
		// 处理注册
		async handleRegister() {
			// 表单验证
			const valid = await this.$refs.registerForm.validate()
			if (!valid) return
			
			if (!this.agreeTerms) {
				this.$u.toast('请先同意用户协议和隐私政策')
				return
			}
			
			this.registerLoading = true
			
			try {
				const registerData = {
					username: this.form.username,
					phone: this.form.phone,
					code: this.form.code,
					password: this.form.password
				}
				
				const result = await this.$http.post('/api/register', registerData)
				
				if (result.code === 200) {
					this.$u.toast('注册成功')
					
					// 注册成功后自动登录
					setTimeout(() => {
						this.goLogin()
					}, 1500)
				} else {
					this.$u.toast(result.message || '注册失败')
				}
			} catch (error) {
				console.error('注册失败:', error)
				this.$u.toast('注册失败，请重试')
			} finally {
				this.registerLoading = false
			}
		},
		
		// 显示用户协议
		showTerms() {
			uni.navigateTo({
				url: '/pages/terms/terms'
			})
		},
		
		// 显示隐私政策
		showPrivacy() {
			uni.navigateTo({
				url: '/pages/privacy/privacy'
			})
		},
		
		// 去登录
		goLogin() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.register-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 100rpx 60rpx 60rpx;
	
	.register-header {
		text-align: center;
		margin-bottom: 80rpx;
		
		.title {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			margin-bottom: 20rpx;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}
	
	.register-form {
		background: #fff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		margin-bottom: 60rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
		
		.code-input {
			display: flex;
			align-items: center;
			gap: 20rpx;
			
			.code-btn {
				flex-shrink: 0;
				width: 160rpx;
				height: 60rpx;
			}
		}
		
		.agreement {
			display: flex;
			align-items: center;
			margin: 40rpx 0;
			font-size: 28rpx;
			
			.link-text {
				color: #3c9cff;
				margin: 0 10rpx;
			}
		}
		
		.register-btn {
			margin-top: 40rpx;
			height: 88rpx;
			border-radius: 44rpx;
		}
	}
	
	.login-link {
		text-align: center;
		
		text {
			color: rgba(255, 255, 255, 0.8);
			font-size: 28rpx;
		}
		
		.link-text {
			color: #fff;
			font-weight: bold;
			margin-left: 10rpx;
		}
	}
}
</style>
