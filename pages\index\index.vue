<template>
	<view class="index-container">
		<!-- 顶部用户信息 -->
		<view class="user-header" v-if="getLoginStatus">
			<u-avatar :src="getUserInfo.avatar || '/static/images/default-avatar.png'" size="60"></u-avatar>
			<view class="user-info">
				<text class="username">{{ getUserInfo.nickname || '用户' }}</text>
				<text class="welcome">欢迎使用UniApp模板</text>
			</view>
		</view>
		
		<!-- 未登录状态 -->
		<view class="login-prompt" v-else>
			<u-icon name="account" size="60" color="#ccc"></u-icon>
			<text class="prompt-text">请先登录</text>
			<u-button type="primary" size="medium" @click="goLogin">立即登录</u-button>
		</view>
		
		<!-- 功能网格 -->
		<view class="function-grid">
			<view class="grid-item" v-for="item in functionList" :key="item.id" @click="handleFunction(item)">
				<u-icon :name="item.icon" size="40" :color="item.color"></u-icon>
				<text class="item-title">{{ item.title }}</text>
				<text class="item-desc">{{ item.desc }}</text>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<text class="section-title">快捷操作</text>
			<view class="action-list">
				<u-cell-group>
					<u-cell 
						v-for="action in quickActions" 
						:key="action.id"
						:title="action.title"
						:label="action.desc"
						:icon="action.icon"
						:rightIcon="action.rightIcon"
						@click="handleAction(action)"
					></u-cell>
				</u-cell-group>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="statistics" v-if="getLoginStatus">
			<text class="section-title">数据统计</text>
			<view class="stats-grid">
				<view class="stat-item" v-for="stat in statistics" :key="stat.id">
					<text class="stat-value">{{ stat.value }}</text>
					<text class="stat-label">{{ stat.label }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			functionList: [
				{
					id: 1,
					title: '网络请求',
					desc: '演示HTTP请求',
					icon: 'wifi',
					color: '#3c9cff',
					action: 'demo'
				},
				{
					id: 2,
					title: '工具函数',
					desc: '常用工具演示',
					icon: 'setting',
					color: '#5ac725',
					action: 'utils'
				},
				{
					id: 3,
					title: 'UI组件',
					desc: 'uView组件展示',
					icon: 'grid',
					color: '#ffb800',
					action: 'components'
				},
				{
					id: 4,
					title: '个人中心',
					desc: '用户信息管理',
					icon: 'account',
					color: '#f56c6c',
					action: 'user'
				}
			],
			quickActions: [
				{
					id: 1,
					title: '扫一扫',
					desc: '扫描二维码',
					icon: 'scan',
					rightIcon: 'arrow-right',
					action: 'scan'
				},
				{
					id: 2,
					title: '意见反馈',
					desc: '提交您的建议',
					icon: 'chat',
					rightIcon: 'arrow-right',
					action: 'feedback'
				},
				{
					id: 3,
					title: '关于我们',
					desc: '了解更多信息',
					icon: 'info',
					rightIcon: 'arrow-right',
					action: 'about'
				}
			],
			statistics: [
				{
					id: 1,
					label: '今日访问',
					value: '128'
				},
				{
					id: 2,
					label: '本周访问',
					value: '856'
				},
				{
					id: 3,
					label: '总访问量',
					value: '12,345'
				},
				{
					id: 4,
					label: '用户数量',
					value: '2,468'
				}
			]
		}
	},
	
	onLoad() {
		this.loadData()
	},
	
	onShow() {
		// 页面显示时刷新数据
		if (this.getLoginStatus) {
			this.loadStatistics()
		}
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.loadData().finally(() => {
			uni.stopPullDownRefresh()
		})
	},
	
	methods: {
		// 加载数据
		async loadData() {
			try {
				// 这里可以加载首页数据
				console.log('加载首页数据')
			} catch (error) {
				console.error('加载数据失败:', error)
			}
		},
		
		// 加载统计数据
		async loadStatistics() {
			try {
				// 模拟请求统计数据
				const result = await this.$http.get('/api/statistics')
				if (result.data) {
					this.statistics = result.data
				}
			} catch (error) {
				console.error('加载统计数据失败:', error)
			}
		},
		
		// 处理功能点击
		handleFunction(item) {
			switch (item.action) {
				case 'demo':
					this.switchTab('/pages/demo/demo')
					break
				case 'utils':
					this.navigateTo('/pages/demo/demo', { tab: 'utils' })
					break
				case 'components':
					this.navigateTo('/pages/demo/demo', { tab: 'components' })
					break
				case 'user':
					if (this.checkLogin()) {
						this.switchTab('/pages/user/user')
					}
					break
			}
		},
		
		// 处理快捷操作
		handleAction(action) {
			switch (action.action) {
				case 'scan':
					this.scanCode()
					break
				case 'feedback':
					this.navigateTo('/pages/feedback/feedback')
					break
				case 'about':
					this.navigateTo('/pages/about/about')
					break
			}
		},
		
		// 扫码功能
		scanCode() {
			// #ifdef APP-PLUS || MP-WEIXIN
			uni.scanCode({
				success: (res) => {
					console.log('扫码结果:', res)
					this.showToast('扫码成功: ' + res.result)
				},
				fail: (error) => {
					console.error('扫码失败:', error)
					this.showToast('扫码失败')
				}
			})
			// #endif
			
			// #ifdef H5
			this.showToast('H5暂不支持扫码功能')
			// #endif
		}
	}
}
</script>

<style lang="scss" scoped>
.index-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.user-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	color: #fff;
	
	.user-info {
		margin-left: 30rpx;
		flex: 1;
		
		.username {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
		}
		
		.welcome {
			font-size: 28rpx;
			opacity: 0.8;
		}
	}
}

.login-prompt {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 80rpx 40rpx;
	text-align: center;
	color: #fff;
	
	.prompt-text {
		display: block;
		font-size: 32rpx;
		margin: 30rpx 0 40rpx;
	}
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	padding: 40rpx 30rpx;
	
	.grid-item {
		background: #fff;
		border-radius: 16rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		
		.item-title {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			margin: 20rpx 0 10rpx;
			color: #333;
		}
		
		.item-desc {
			font-size: 24rpx;
			color: #999;
		}
	}
}

.quick-actions, .statistics {
	margin: 0 30rpx 40rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.action-list {
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
	}
}

.statistics {
	.stats-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
		
		.stat-item {
			background: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			text-align: center;
			
			.stat-value {
				display: block;
				font-size: 48rpx;
				font-weight: bold;
				color: #3c9cff;
				margin-bottom: 10rpx;
			}
			
			.stat-label {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
}
</style>
