<template>
	<div id="app">
		<!-- 这里是应用的根组件 -->
	</div>
</template>

<script>
export default {
	onLaunch: function() {
		console.log('App Launch')
		// 应用启动时的逻辑
		this.initApp()
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	},
	methods: {
		initApp() {
			// 初始化应用配置
			this.checkUpdate()
			this.initUserInfo()
		},
		// 检查更新
		checkUpdate() {
			// #ifdef APP-PLUS
			const updateManager = plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
				console.log('当前版本：' + widgetInfo.version)
			})
			// #endif
		},
		// 初始化用户信息
		initUserInfo() {
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo) {
				this.$store.commit('user/setUserInfo', userInfo)
			}
		}
	}
}
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "uview-ui/index.scss";
	
	/* 全局样式 */
	page {
		background-color: #f5f5f5;
	}
	
	/* 自定义样式 */
	.container {
		padding: 20rpx;
	}
	
	.page-content {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
</style>
