#!/usr/bin/env node

/**
 * UniApp 项目启动脚本
 * 用于在没有完整 UniApp CLI 环境的情况下启动项目
 */

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

console.log('🚀 UniApp Vue2 APP开发脚手架模板')
console.log('📱 专门针对APP开发优化')
console.log('📦 正在检查项目依赖...')

// 检查必要文件
const requiredFiles = [
  'package.json',
  'main.js',
  'App.vue',
  'pages.json',
  'manifest.json'
]

const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))

if (missingFiles.length > 0) {
  console.error('❌ 缺少必要文件:', missingFiles.join(', '))
  process.exit(1)
}

// 检查 node_modules
if (!fs.existsSync('node_modules')) {
  console.log('📦 正在安装依赖...')
  const install = spawn('npm', ['install'], { stdio: 'inherit' })
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 依赖安装完成')
      startProject()
    } else {
      console.error('❌ 依赖安装失败')
      process.exit(1)
    }
  })
} else {
  startProject()
}

function startProject() {
  console.log('\n🎯 项目功能说明:')
  console.log('  ✅ 完整的登录注册系统')
  console.log('  ✅ uView UI 组件库集成')
  console.log('  ✅ 公共 HTTP 请求封装')
  console.log('  ✅ Vuex 状态管理')
  console.log('  ✅ 常用工具函数')
  console.log('  ✅ 全局混入和样式')

  console.log('\n🚨 重要提示:')
  console.log('  如果遇到 "uni-serve" 命令不存在的错误，这是正常的！')
  console.log('  UniApp 需要特定的开发环境。')

  console.log('\n🛠️  解决方案:')
  console.log('  方案一 (推荐): 使用 HBuilderX')
  console.log('    1. 下载: https://www.dcloud.io/hbuilderx.html')
  console.log('    2. 用 HBuilderX 打开项目文件夹')
  console.log('    3. 点击运行按钮')

  console.log('\n  方案二: 安装 uni-app CLI')
  console.log('    1. npm install -g @vue/cli')
  console.log('    2. vue add @dcloudio/uni-app')
  console.log('    3. npm run dev:h5')

  console.log('\n  方案三: 预览项目结构')
  console.log('    npm run preview  (查看项目说明)')

  console.log('\n📋 可用的开发命令:')
  console.log('  npm run dev:app       - APP 开发模式 (主要)')
  console.log('  npm run dev:h5        - H5 开发模式 (调试用)')
  console.log('  npm run serve         - 默认启动APP开发')

  console.log('\n📋 可用的构建命令:')
  console.log('  npm run build:app     - APP 生产构建')
  console.log('  npm run build:h5      - H5 生产构建')
  console.log('  npm run build         - 默认构建APP')

  console.log('\n📋 辅助命令:')
  console.log('  npm run preview       - 启动预览服务器')
  console.log('  npm run setup-hbuilderx - 查看 HBuilderX 下载链接')
  console.log('  npm run install-uni   - 安装 uni-app CLI')
  console.log('  npm run info          - 查看项目信息')

  console.log('\n⚠️  配置提醒:')
  console.log('  1. 请替换 static/images/ 目录下的占位图片')
  console.log('  2. 请替换 static/tabbar/ 目录下的占位图标')
  console.log('  3. 请修改 utils/config.js 中的 API 配置')

  console.log('\n📖 详细说明请查看:')
  console.log('  - README.md (项目文档)')
  console.log('  - SETUP_GUIDE.md (安装指南)')
  console.log('  - PROJECT_SUMMARY.md (项目总结)')

  console.log('\n🎉 项目代码已完整，选择合适的开发环境即可开始！')
}
