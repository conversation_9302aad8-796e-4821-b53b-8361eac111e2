#!/usr/bin/env node

/**
 * UniApp 项目启动脚本
 * 用于在没有完整 UniApp CLI 环境的情况下启动项目
 */

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

console.log('🚀 UniApp Vue2 脚手架模板')
console.log('📦 正在检查项目依赖...')

// 检查必要文件
const requiredFiles = [
  'package.json',
  'main.js',
  'App.vue',
  'pages.json',
  'manifest.json'
]

const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))

if (missingFiles.length > 0) {
  console.error('❌ 缺少必要文件:', missingFiles.join(', '))
  process.exit(1)
}

// 检查 node_modules
if (!fs.existsSync('node_modules')) {
  console.log('📦 正在安装依赖...')
  const install = spawn('npm', ['install'], { stdio: 'inherit' })
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 依赖安装完成')
      startProject()
    } else {
      console.error('❌ 依赖安装失败')
      process.exit(1)
    }
  })
} else {
  startProject()
}

function startProject() {
  console.log('\n🎯 项目功能说明:')
  console.log('  ✅ 完整的登录注册系统')
  console.log('  ✅ uView UI 组件库集成')
  console.log('  ✅ 公共 HTTP 请求封装')
  console.log('  ✅ Vuex 状态管理')
  console.log('  ✅ 常用工具函数')
  console.log('  ✅ 全局混入和样式')
  
  console.log('\n📋 可用命令:')
  console.log('  npm run dev:h5        - H5 开发模式')
  console.log('  npm run dev:mp-weixin - 微信小程序开发模式')
  console.log('  npm run build:h5      - H5 生产构建')
  console.log('  npm run build:mp-weixin - 微信小程序生产构建')
  
  console.log('\n⚠️  注意事项:')
  console.log('  1. 请替换 static/images/ 目录下的占位图片')
  console.log('  2. 请替换 static/tabbar/ 目录下的占位图标')
  console.log('  3. 请修改 utils/config.js 中的 API 配置')
  console.log('  4. 建议使用 HBuilderX 或 uni-app CLI 获得完整功能')
  
  console.log('\n🔧 推荐开发环境:')
  console.log('  - HBuilderX: https://www.dcloud.io/hbuilderx.html')
  console.log('  - uni-app CLI: npm install -g @vue/cli @dcloudio/uvm')
  
  console.log('\n📖 更多信息请查看 README.md')
  console.log('\n🎉 项目已准备就绪！')
}
