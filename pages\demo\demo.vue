<template>
	<view class="demo-container">
		<view class="demo-header">
			<text class="title">功能演示</text>
		</view>
		
		<view class="demo-content">
			<!-- 网络请求演示 -->
			<view class="demo-section">
				<text class="section-title">网络请求演示</text>
				<u-button type="primary" @click="testRequest">测试GET请求</u-button>
				<u-button type="success" @click="testPost" style="margin-top: 20rpx;">测试POST请求</u-button>
			</view>
			
			<!-- 工具函数演示 -->
			<view class="demo-section">
				<text class="section-title">工具函数演示</text>
				<u-button type="warning" @click="testUtils">测试工具函数</u-button>
			</view>
			
			<!-- UI组件演示 -->
			<view class="demo-section">
				<text class="section-title">UI组件演示</text>
				<u-button type="info" @click="showModal">显示模态框</u-button>
				<u-button type="error" @click="showToast" style="margin-top: 20rpx;">显示提示</u-button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		}
	},
	
	methods: {
		// 测试GET请求
		async testRequest() {
			try {
				// 这里使用一个公开的测试API
				const result = await this.$http.get('https://jsonplaceholder.typicode.com/posts/1', {}, {
					showLoading: true
				})
				console.log('GET请求结果:', result)
				uni.showToast({
					title: '请求成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('请求失败:', error)
			}
		},
		
		// 测试POST请求
		async testPost() {
			try {
				const result = await this.$http.post('https://jsonplaceholder.typicode.com/posts', {
					title: 'test',
					body: 'test body',
					userId: 1
				})
				console.log('POST请求结果:', result)
				uni.showToast({
					title: '请求成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('请求失败:', error)
			}
		},
		
		// 测试工具函数
		testUtils() {
			const now = new Date()
			const formatted = this.$utils.formatDate(now)
			const timeAgo = this.$utils.timeAgo(new Date(Date.now() - 60000))
			
			uni.showModal({
				title: '工具函数测试',
				content: `当前时间: ${formatted}\n一分钟前: ${timeAgo}`,
				showCancel: false
			})
		},
		
		// 显示模态框
		showModal() {
			uni.showModal({
				title: '提示',
				content: '这是一个模态框',
				success: (res) => {
					if (res.confirm) {
						console.log('用户点击确定')
					}
				}
			})
		},
		
		// 显示提示
		showToast() {
			uni.showToast({
				title: '这是一个提示',
				icon: 'none'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.demo-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.demo-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
	color: #fff;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
	}
}

.demo-content {
	padding: 40rpx 30rpx;
}

.demo-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}
}
</style>
