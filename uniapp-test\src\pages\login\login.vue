<template>
	<view class="login-container">
		<view class="login-header">
			<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
			<text class="app-name">{{ appName }}</text>
		</view>
		
		<view class="login-form">
			<u-form :model="form" ref="loginForm" :rules="rules">
				<u-form-item prop="username" borderBottom>
					<u-input 
						v-model="form.username" 
						placeholder="请输入用户名/手机号" 
						prefixIcon="account"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="password" borderBottom>
					<u-input 
						v-model="form.password" 
						type="password" 
						placeholder="请输入密码" 
						prefixIcon="lock"
						:passwordIcon="true"
						clearable
					/>
				</u-form-item>
				
				<view class="login-options">
					<u-checkbox v-model="rememberPassword">记住密码</u-checkbox>
					<text class="forgot-password" @click="forgotPassword">忘记密码？</text>
				</view>
				
				<u-button 
					type="primary" 
					:loading="loginLoading"
					@click="handleLogin"
					class="login-btn"
				>
					登录
				</u-button>
			</u-form>
		</view>
		
		<view class="third-party-login">
			<view class="divider">
				<text>其他登录方式</text>
			</view>
			
			<view class="third-party-buttons">
				<!-- #ifdef MP-WEIXIN -->
				<u-button 
					type="success" 
					size="medium"
					@click="wechatLogin"
					class="third-btn"
				>
					<u-icon name="weixin-fill" size="20" color="#fff" style="margin-right: 8rpx;"></u-icon>
					微信登录
				</u-button>
				<!-- #endif -->
				
				<!-- #ifdef APP-PLUS -->
				<u-button 
					type="warning" 
					size="medium"
					@click="qqLogin"
					class="third-btn"
				>
					<u-icon name="qq-fill" size="20" color="#fff" style="margin-right: 8rpx;"></u-icon>
					QQ登录
				</u-button>
				<!-- #endif -->
			</view>
		</view>
		
		<view class="register-link">
			<text>还没有账号？</text>
			<text class="link-text" @click="goRegister">立即注册</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp模板',
			loginLoading: false,
			rememberPassword: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: 'blur'
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: 'blur'
					}
				]
			}
		}
	},
	
	onLoad() {
		this.loadRememberedPassword()
	},
	
	methods: {
		// 处理登录
		async handleLogin() {
			// 表单验证
			const valid = await this.$refs.loginForm.validate()
			if (!valid) return
			
			this.loginLoading = true
			
			try {
				const loginData = {
					username: this.form.username,
					password: this.form.password
				}
				
				// 调用登录接口
				const result = await this.$http.post('/api/login', loginData)
				
				if (result.code === 200) {
					// 保存用户信息和token
					await this.saveUserInfo(result.data)
					
					// 记住密码
					if (this.rememberPassword) {
						this.savePassword()
					} else {
						this.clearPassword()
					}
					
					this.$u.toast('登录成功')
					
					// 跳转到首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					this.$u.toast(result.message || '登录失败')
				}
			} catch (error) {
				console.error('登录失败:', error)
				this.$u.toast('登录失败，请重试')
			} finally {
				this.loginLoading = false
			}
		},
		
		// 微信登录
		wechatLogin() {
			// #ifdef MP-WEIXIN
			uni.login({
				provider: 'weixin',
				success: (loginRes) => {
					console.log('微信登录成功:', loginRes)
					// 获取用户信息
					uni.getUserInfo({
						provider: 'weixin',
						success: (infoRes) => {
							console.log('用户信息:', infoRes)
							// 调用后端接口进行微信登录
							this.thirdPartyLogin('wechat', {
								code: loginRes.code,
								userInfo: infoRes.userInfo
							})
						}
					})
				},
				fail: (error) => {
					console.error('微信登录失败:', error)
					this.$u.toast('微信登录失败')
				}
			})
			// #endif
		},
		
		// QQ登录
		qqLogin() {
			// #ifdef APP-PLUS
			uni.login({
				provider: 'qq',
				success: (loginRes) => {
					console.log('QQ登录成功:', loginRes)
					this.thirdPartyLogin('qq', loginRes)
				},
				fail: (error) => {
					console.error('QQ登录失败:', error)
					this.$u.toast('QQ登录失败')
				}
			})
			// #endif
		},
		
		// 第三方登录
		async thirdPartyLogin(type, data) {
			try {
				const result = await this.$http.post('/api/third-party-login', {
					type,
					data
				})
				
				if (result.code === 200) {
					await this.saveUserInfo(result.data)
					this.$u.toast('登录成功')
					
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					this.$u.toast(result.message || '登录失败')
				}
			} catch (error) {
				console.error('第三方登录失败:', error)
				this.$u.toast('登录失败，请重试')
			}
		},
		
		// 保存用户信息
		async saveUserInfo(userData) {
			// 保存到本地存储
			uni.setStorageSync('token', userData.token)
			uni.setStorageSync('userInfo', userData.userInfo)
			
			// 保存到Vuex
			this.$store.commit('user/setToken', userData.token)
			this.$store.commit('user/setUserInfo', userData.userInfo)
		},
		
		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', {
				username: this.form.username,
				password: this.form.password
			})
		},
		
		// 清除保存的密码
		clearPassword() {
			uni.removeStorageSync('rememberedPassword')
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('rememberedPassword')
			if (remembered) {
				this.form.username = remembered.username
				this.form.password = remembered.password
				this.rememberPassword = true
			}
		},
		
		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},
		
		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>
