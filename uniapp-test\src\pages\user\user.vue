<template>
	<view class="user-container">
		<view class="user-header">
			<view class="user-info" v-if="userInfo">
				<u-avatar :src="userInfo.avatar" size="80"></u-avatar>
				<view class="info-text">
					<text class="username">{{ userInfo.username }}</text>
					<text class="phone">{{ userInfo.phone }}</text>
				</view>
			</view>
			<view class="login-prompt" v-else @click="goLogin">
				<u-avatar size="80"></u-avatar>
				<text class="login-text">点击登录</text>
			</view>
		</view>
		
		<view class="user-menu">
			<u-cell-group>
				<u-cell 
					title="个人资料" 
					icon="account" 
					isLink 
					@click="goProfile"
				></u-cell>
				
				<u-cell 
					title="修改密码" 
					icon="lock" 
					isLink 
					@click="goChangePassword"
				></u-cell>
				
				<u-cell 
					title="消息通知" 
					icon="bell" 
					isLink 
					@click="goNotification"
				></u-cell>
				
				<u-cell 
					title="隐私设置" 
					icon="setting" 
					isLink 
					@click="goPrivacySetting"
				></u-cell>
				
				<u-cell 
					title="关于我们" 
					icon="info-circle" 
					isLink 
					@click="goAbout"
				></u-cell>
				
				<u-cell 
					title="意见反馈" 
					icon="chat" 
					isLink 
					@click="goFeedback"
				></u-cell>
			</u-cell-group>
		</view>
		
		<view class="logout-section" v-if="userInfo">
			<u-button 
				type="error" 
				@click="handleLogout"
				class="logout-btn"
			>
				退出登录
			</u-button>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex'

export default {
	computed: {
		...mapState('user', ['userInfo', 'token'])
	},
	
	onShow() {
		// 页面显示时检查登录状态
		this.checkLoginStatus()
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token')
			const userInfo = uni.getStorageSync('userInfo')
			
			if (token && userInfo) {
				this.$store.commit('user/setToken', token)
				this.$store.commit('user/setUserInfo', userInfo)
			}
		},
		
		// 去登录
		goLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		},
		
		// 去个人资料
		goProfile() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/profile/profile'
			})
		},
		
		// 去修改密码
		goChangePassword() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/change-password/change-password'
			})
		},
		
		// 去消息通知
		goNotification() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/notification/notification'
			})
		},
		
		// 去隐私设置
		goPrivacySetting() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/privacy-setting/privacy-setting'
			})
		},
		
		// 去关于我们
		goAbout() {
			uni.navigateTo({
				url: '/pages/about/about'
			})
		},
		
		// 去意见反馈
		goFeedback() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/feedback/feedback'
			})
		},
		
		// 检查认证状态
		checkAuth() {
			if (!this.token || !this.userInfo) {
				this.$u.toast('请先登录')
				setTimeout(() => {
					this.goLogin()
				}, 1500)
				return false
			}
			return true
		},
		
		// 处理退出登录
		handleLogout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.logout()
					}
				}
			})
		},
		
		// 退出登录
		async logout() {
			try {
				// 调用退出登录接口
				await this.$http.post('/api/logout')
			} catch (error) {
				console.error('退出登录失败:', error)
			} finally {
				// 清除本地存储
				uni.removeStorageSync('token')
				uni.removeStorageSync('userInfo')
				
				// 清除Vuex状态
				this.$store.commit('user/setToken', '')
				this.$store.commit('user/setUserInfo', null)
				
				this.$u.toast('已退出登录')
				
				// 跳转到登录页
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
			}
		}
	}
}
</script>
