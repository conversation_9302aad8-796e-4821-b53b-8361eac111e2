# UniApp Vue2 脚手架模板

这是一个基于 UniApp + Vue2 的脚手架模板，集成了 uView UI 组件库和公共请求封装。

## 🚨 关于 "uni-serve" 命令不存在的问题

如果您遇到 `command "uni-serve" does not exist` 错误，**这是正常的**！

UniApp 需要特定的开发环境，请查看下面的解决方案。

## 功能特性

- ✅ 基于 UniApp + Vue2
- ✅ 集成 uView UI 组件库
- ✅ 封装公共 HTTP 请求模块
- ✅ 完整的登录注册流程
- ✅ Vuex 状态管理
- ✅ 常用工具函数
- ✅ 全局混入
- ✅ 统一的样式规范

## 项目结构

```
├── pages/                  # 页面目录
│   ├── index/              # 首页
│   ├── login/              # 登录页
│   ├── register/           # 注册页
│   ├── forgot-password/    # 忘记密码页
│   ├── demo/               # 功能演示页
│   └── user/               # 个人中心页
├── components/             # 组件目录
├── static/                 # 静态资源
│   ├── css/               # 样式文件
│   ├── images/            # 图片资源
│   └── tabbar/            # tabbar图标
├── store/                  # Vuex状态管理
│   ├── index.js           # store入口
│   └── modules/           # 模块
│       └── user.js        # 用户模块
├── utils/                  # 工具函数
│   ├── index.js           # 工具函数集合
│   ├── request.js         # HTTP请求封装
│   └── config.js          # 配置文件
├── mixins/                 # 混入
│   └── global.js          # 全局混入
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── uni.scss               # 全局样式变量
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 查看项目说明

```bash
npm start
```

### 3. 预览项目结构

```bash
npm run preview
```

然后在浏览器中访问 http://localhost:3000

## 🛠️ 解决 "uni-serve" 问题

### 方案一：使用 HBuilderX (强烈推荐)

1. **下载 HBuilderX**
   ```bash
   npm run setup-hbuilderx  # 查看下载链接
   ```
   或直接访问：https://www.dcloud.io/hbuilderx.html

2. **打开项目**
   - 启动 HBuilderX
   - 文件 → 打开目录 → 选择当前项目文件夹

3. **运行项目**
   - 点击工具栏的"运行"按钮
   - 选择运行到浏览器/小程序/手机模拟器

### 方案二：使用 uni-app CLI

```bash
# 安装 uni-app CLI
npm run install-uni

# 或手动安装
npm install -g @vue/cli
vue add @dcloudio/uni-app

# 然后运行项目
npm run dev:h5        # H5 开发
npm run dev:mp-weixin # 微信小程序开发
```

### 方案三：当前项目预览

如果只想查看项目结构：

```bash
npm run preview  # 启动预览服务器
```

## 📋 可用命令

```bash
npm start              # 查看项目说明
npm run preview        # 启动预览服务器
npm run setup-hbuilderx # 查看 HBuilderX 下载链接
npm run install-uni    # 安装 uni-app CLI
```

## 构建项目

### H5
```bash
npm run build:h5
```

### 微信小程序
```bash
npm run build:mp-weixin
```

### APP
```bash
npm run build:app-plus
```

## 主要功能

### 1. 登录注册
- 用户名密码登录
- 手机号注册
- 忘记密码
- 第三方登录（微信、QQ）

### 2. 网络请求
- 基于 uni.request 封装
- 请求/响应拦截器
- 自动处理 loading
- 统一错误处理
- 支持文件上传

### 3. 状态管理
- 用户信息管理
- 登录状态管理
- 本地存储同步

### 4. 工具函数
- 时间格式化
- 表单验证
- 防抖节流
- 深拷贝
- 文件处理等

### 5. UI组件
- 集成 uView UI 2.0
- 统一的样式规范
- 响应式布局

## 配置说明

### 1. 环境配置
在 `utils/config.js` 中配置不同环境的 API 地址：

```javascript
const ENV = {
  development: {
    baseURL: 'https://api-dev.example.com',
    timeout: 10000
  },
  production: {
    baseURL: 'https://api.example.com',
    timeout: 15000
  }
}
```

### 2. 第三方配置
在 `utils/config.js` 中配置第三方应用信息：

```javascript
config.thirdParty = {
  wechat: {
    appId: 'your-wechat-appid'
  },
  qq: {
    appId: 'your-qq-appid'
  }
}
```

## 使用说明

### 1. 发起网络请求
```javascript
// GET请求
const result = await this.$http.get('/api/user/info')

// POST请求
const result = await this.$http.post('/api/login', {
  username: 'test',
  password: '123456'
})

// 文件上传
const result = await this.$http.upload('/api/upload', filePath)
```

### 2. 使用工具函数
```javascript
// 格式化时间
const formatted = this.$utils.formatDate(new Date())

// 表单验证
const isValid = this.$utils.validatePhone('13800138000')

// 显示提示
this.$utils.showToast('操作成功')
```

### 3. 状态管理
```javascript
// 获取用户信息
const userInfo = this.$store.getters['user/getUserInfo']

// 设置用户信息
this.$store.commit('user/setUserInfo', userInfo)

// 退出登录
this.$store.dispatch('user/logout')
```

## 注意事项

1. 请替换 `static/images/` 目录下的占位图片为实际图片
2. 请替换 `static/tabbar/` 目录下的占位图标为实际图标
3. 请根据实际需求修改 `utils/config.js` 中的配置
4. 请根据后端接口调整请求和响应的数据结构

## 许可证

MIT License
