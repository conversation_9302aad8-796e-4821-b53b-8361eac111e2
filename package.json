{"name": "uniapp-vue2-template", "version": "1.0.0", "description": "UniApp Vue2 脚手架模板，集成uview UI和公共请求封装", "main": "main.js", "scripts": {"start": "node start.js", "preview": "node dev-server.js", "serve": "npm run preview", "hbuilderx": "echo 请使用 HBuilderX 打开项目进行 APP 开发", "dev:app": "echo 请使用 HBuilderX 运行到手机或模拟器", "build:app": "echo 请使用 HBuilderX 发行为原生APP", "dev:h5": "echo 请使用 HBuilderX 运行到浏览器", "build:h5": "echo 请使用 HBuilderX 发行为H5", "install-hbuilderx": "echo 下载地址: https://www.dcloud.io/hbuilderx.html", "info": "echo 'UniApp APP 开发脚手架 - 请使用 HBuilderX 进行开发'"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3081220230817001", "@dcloudio/uni-h5": "^2.0.2-3081220230817001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-stat": "^2.0.2-3081220230817001", "@vue/shared": "^3.0.0", "core-js": "^3.8.3", "regenerator-runtime": "^0.12.1", "uview-ui": "^2.0.36", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-cli-shared": "^2.0.2-3081220230817001", "@dcloudio/uni-template-compiler": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3081220230817001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3081220230817001", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.3", "express": "^4.18.2", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}