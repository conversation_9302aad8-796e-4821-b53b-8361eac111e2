{"name": "uniapp-vue2-template", "version": "1.0.0", "description": "UniApp Vue2 脚手架模板，集成uview UI和公共请求封装", "main": "main.js", "scripts": {"start": "node start.js", "preview": "node dev-server.js", "serve": "npm run dev:app", "build": "npm run build:app", "dev:app": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "build:app": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest", "info": "echo 'UniApp APP 开发环境配置完成'"}, "dependencies": {"@dcloudio/uni-app": "2.0.2-3081220230817001", "@dcloudio/uni-h5": "2.0.2-3081220230817001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-stat": "2.0.2-3081220230817001", "@vue/shared": "^3.0.0", "core-js": "^3.8.3", "regenerator-runtime": "^0.12.1", "uview-ui": "^2.0.36", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-cli-shared": "2.0.2-3081220230817001", "@dcloudio/uni-template-compiler": "2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni": "2.0.2-3081220230817001", "@dcloudio/webpack-uni-pages-loader": "2.0.2-3081220230817001", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.3", "express": "^4.18.2", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}