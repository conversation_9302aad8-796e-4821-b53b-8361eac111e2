{"name": "uniapp-vue2-template", "version": "1.0.0", "description": "UniApp Vue2 脚手架模板，集成uview UI和公共请求封装", "main": "main.js", "scripts": {"start": "node start.js", "preview": "node dev-server.js", "serve": "npm run dev:h5", "install-uni": "npm install -g @vue/cli && vue add @dcloudio/uni-app", "setup-hbuilderx": "echo 请下载 HBuilderX: https://www.dcloud.io/hbuilderx.html", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest", "test:h5": "cross-env UNI_PLATFORM=h5 jest", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3081220230817001", "@dcloudio/uni-h5": "^2.0.2-3081220230817001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-alipay": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-baidu": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-qq": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-weixin": "^2.0.2-3081220230817001", "@dcloudio/uni-quickapp-native": "^2.0.2-3081220230817001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3081220230817001", "@dcloudio/uni-stat": "^2.0.2-3081220230817001", "@vue/shared": "^3.0.0", "core-js": "^3.8.3", "flyio": "^0.6.2", "regenerator-runtime": "^0.12.1", "uview-ui": "^2.0.36", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.2-3081220230817001", "@dcloudio/uni-cli-shared": "^2.0.2-3081220230817001", "@dcloudio/uni-template-compiler": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3081220230817001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3081220230817001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3081220230817001", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.3", "express": "^4.18.2", "jest": "^25.4.0", "mini-types": "*", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}