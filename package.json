{"name": "uniapp-vue2-template", "version": "1.0.0", "description": "UniApp Vue2 脚手架模板，集成uview UI和公共请求封装", "main": "main.js", "scripts": {"start": "node start.js", "preview": "node dev-server.js", "serve": "npm run preview", "install-uni": "npm install -g @vue/cli && vue add @dcloudio/uni-app", "setup-hbuilderx": "echo 请下载 HBuilderX: https://www.dcloud.io/hbuilderx.html", "dev:h5": "echo 请使用 HBuilderX 或先运行 npm run install-uni 安装 UniApp CLI", "dev:mp-weixin": "echo 请使用 HBuilderX 或先运行 npm run install-uni 安装 UniApp CLI", "build:h5": "echo 请使用 HBuilderX 或先运行 npm run install-uni 安装 UniApp CLI", "build:mp-weixin": "echo 请使用 HBuilderX 或先运行 npm run install-uni 安装 UniApp CLI"}, "dependencies": {"core-js": "^3.8.3", "regenerator-runtime": "^0.12.1", "uview-ui": "^2.0.36", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"babel-plugin-import": "^1.11.0", "cross-env": "^7.0.3", "express": "^4.18.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}