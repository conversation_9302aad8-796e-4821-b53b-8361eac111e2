import Vue from 'vue'
import App from './App'
import store from './store'

// 引入uView UI
import uView from 'uview-ui'
Vue.use(uView)

// 引入全局样式
import './static/css/global.css'

// 引入公共请求
import http from './utils/request'
Vue.prototype.$http = http

// 引入工具函数
import utils from './utils/index'
Vue.prototype.$utils = utils

// 引入全局混入
import globalMixin from './mixins/global'
Vue.mixin(globalMixin)

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})
app.$mount()
