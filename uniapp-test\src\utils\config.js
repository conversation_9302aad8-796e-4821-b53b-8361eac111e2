/**
 * 应用配置文件
 */

// 环境配置
const ENV = {
	// 开发环境
	development: {
		baseURL: 'https://api-dev.example.com',
		timeout: 10000,
		debug: true
	},
	// 测试环境
	testing: {
		baseURL: 'https://api-test.example.com',
		timeout: 10000,
		debug: true
	},
	// 生产环境
	production: {
		baseURL: 'https://api.example.com',
		timeout: 15000,
		debug: false
	}
}

// 当前环境
const currentEnv = process.env.NODE_ENV || 'development'

// 导出当前环境配置
const config = ENV[currentEnv]

// 应用信息
config.appInfo = {
	name: 'UniApp模板',
	version: '1.0.0',
	description: 'UniApp Vue2 脚手架模板'
}

// 存储键名
config.storageKeys = {
	token: 'token',
	userInfo: 'userInfo',
	settings: 'settings'
}

// 页面路径
config.pages = {
	login: '/pages/login/login',
	index: '/pages/index/index',
	user: '/pages/user/user'
}

// 默认头像
config.defaultAvatar = '/static/images/default-avatar.png'

// 文件上传配置
config.upload = {
	maxSize: 10 * 1024 * 1024, // 10MB
	allowTypes: ['jpg', 'jpeg', 'png', 'gif'],
	uploadUrl: '/api/upload'
}

// 分页配置
config.pagination = {
	pageSize: 20,
	pageSizeOptions: [10, 20, 50, 100]
}

// 缓存配置
config.cache = {
	// 缓存过期时间（毫秒）
	expireTime: 30 * 60 * 1000, // 30分钟
	// 最大缓存数量
	maxSize: 100
}

// 第三方配置
config.thirdParty = {
	// 微信小程序
	wechat: {
		appId: 'your-wechat-appid'
	},
	// QQ
	qq: {
		appId: 'your-qq-appid'
	}
}

export default config
