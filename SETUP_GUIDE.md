# UniApp 项目设置指南

## 🚨 关于 "uni-serve" 命令不存在的问题

您遇到的 `command "uni-serve" does not exist` 错误是因为缺少 UniApp 的完整开发环境。这是正常的，因为 UniApp 需要特定的开发工具。

## 🛠️ 解决方案

### 方案一：使用 HBuilderX (强烈推荐)

这是 UniApp 官方推荐的开发工具，最简单易用：

1. **下载 HBuilderX**
   - 访问：https://www.dcloud.io/hbuilderx.html
   - 下载并安装 HBuilderX

2. **打开项目**
   - 启动 HBuilderX
   - 文件 → 打开目录 → 选择当前项目文件夹

3. **运行项目**
   - 点击工具栏的"运行"按钮
   - 选择运行到浏览器/小程序/手机模拟器

### 方案二：使用 uni-app CLI

如果您更喜欢命令行开发：

1. **安装 Vue CLI**
   ```bash
   npm install -g @vue/cli
   ```

2. **安装 uni-app 插件**
   ```bash
   vue add @dcloudio/uni-app
   ```

3. **运行项目**
   ```bash
   npm run dev:h5        # H5 开发
   npm run dev:mp-weixin # 微信小程序开发
   ```

### 方案三：当前项目预览

如果您只想查看项目结构和代码：

```bash
npm run preview
```

然后访问 http://localhost:3000 查看项目说明。

## 📋 当前项目状态

✅ **已完成**：
- 完整的项目结构和代码
- 登录注册系统
- uView UI 组件集成
- 公共请求封装
- 状态管理
- 工具函数库

⚠️ **需要 UniApp 环境**：
- 实际运行和调试
- 编译到各个平台
- 完整的开发体验

## 🎯 推荐开发流程

1. **使用 HBuilderX 开发** (最简单)
   - 下载安装 HBuilderX
   - 打开项目文件夹
   - 直接运行和调试

2. **或者使用 CLI** (适合命令行爱好者)
   - 安装 Vue CLI 和 uni-app 插件
   - 使用命令行进行开发

3. **项目配置**
   - 替换 `static/images/` 下的占位图片
   - 修改 `utils/config.js` 中的 API 配置
   - 配置第三方登录信息

## 🔧 常见问题

### Q: 为什么不能直接运行？
A: UniApp 是一个跨平台框架，需要特定的编译工具将 Vue 代码转换为各平台的代码。

### Q: HBuilderX 和 CLI 哪个更好？
A: 
- **HBuilderX**: 官方 IDE，集成度高，适合新手
- **CLI**: 命令行工具，适合有经验的开发者

### Q: 可以用其他编辑器吗？
A: 可以用 VS Code 等编辑器编写代码，但运行和调试仍需要 HBuilderX 或 CLI。

## 📖 相关文档

- [UniApp 官方文档](https://uniapp.dcloud.io/)
- [HBuilderX 使用教程](https://hx.dcloud.net.cn/)
- [uView UI 文档](https://www.uviewui.com/)

## 🚀 快速开始

1. **立即体验**：
   ```bash
   npm run preview
   ```

2. **完整开发**：
   - 下载 HBuilderX
   - 打开项目
   - 开始开发

---

**总结**：您的项目代码是完整的，只需要安装 UniApp 开发环境即可正常运行！
