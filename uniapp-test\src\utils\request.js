/**
 * 网络请求封装
 * 基于uni.request进行封装，支持拦截器、自动处理loading、错误处理等
 */

import config from './config.js'

class Request {
	constructor() {
		this.config = {
			baseURL: config.baseURL,
			timeout: config.timeout || 10000,
			header: {
				'Content-Type': 'application/json;charset=UTF-8'
			}
		}
		
		// 请求队列
		this.requestQueue = []
		// loading状态
		this.isLoading = false
	}
	
	/**
	 * 请求拦截器
	 */
	requestInterceptor(config) {
		// 添加token
		const token = uni.getStorageSync('token')
		if (token) {
			config.header.Authorization = `Bearer ${token}`
		}
		
		// 显示loading
		if (config.showLoading !== false) {
			this.showLoading()
		}
		
		console.log('请求拦截器:', config)
		return config
	}
	
	/**
	 * 响应拦截器
	 */
	responseInterceptor(response, config) {
		// 隐藏loading
		if (config.showLoading !== false) {
			this.hideLoading()
		}
		
		console.log('响应拦截器:', response)
		
		const { data, statusCode } = response
		
		// HTTP状态码处理
		if (statusCode !== 200) {
			this.handleHttpError(statusCode)
			return Promise.reject(response)
		}
		
		// 业务状态码处理
		if (data.code !== undefined) {
			if (data.code === 200 || data.code === 0) {
				return data
			} else {
				this.handleBusinessError(data)
				return Promise.reject(data)
			}
		}
		
		return data
	}
	
	/**
	 * 错误拦截器
	 */
	errorInterceptor(error, config) {
		// 隐藏loading
		if (config.showLoading !== false) {
			this.hideLoading()
		}
		
		console.error('请求错误:', error)
		
		// 网络错误处理
		if (error.errMsg) {
			if (error.errMsg.includes('timeout')) {
				uni.showToast({
					title: '请求超时，请重试',
					icon: 'none'
				})
			} else if (error.errMsg.includes('fail')) {
				uni.showToast({
					title: '网络连接失败',
					icon: 'none'
				})
			}
		}
		
		return Promise.reject(error)
	}
	
	/**
	 * HTTP状态码错误处理
	 */
	handleHttpError(statusCode) {
		let message = '请求失败'
		
		switch (statusCode) {
			case 400:
				message = '请求参数错误'
				break
			case 401:
				message = '未授权，请重新登录'
				this.handleUnauthorized()
				break
			case 403:
				message = '拒绝访问'
				break
			case 404:
				message = '请求地址不存在'
				break
			case 500:
				message = '服务器内部错误'
				break
			case 502:
				message = '网关错误'
				break
			case 503:
				message = '服务不可用'
				break
			case 504:
				message = '网关超时'
				break
			default:
				message = `请求失败(${statusCode})`
		}
		
		uni.showToast({
			title: message,
			icon: 'none'
		})
	}
	
	/**
	 * 业务状态码错误处理
	 */
	handleBusinessError(data) {
		const { code, message } = data
		
		switch (code) {
			case 401:
				this.handleUnauthorized()
				break
			case 403:
				uni.showToast({
					title: message || '没有权限',
					icon: 'none'
				})
				break
			default:
				if (message) {
					uni.showToast({
						title: message,
						icon: 'none'
					})
				}
		}
	}
	
	/**
	 * 处理未授权
	 */
	handleUnauthorized() {
		// 清除本地存储
		uni.removeStorageSync('token')
		uni.removeStorageSync('userInfo')
		
		// 跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})
	}
	
	/**
	 * 显示loading
	 */
	showLoading() {
		if (!this.isLoading) {
			this.isLoading = true
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
		}
	}
	
	/**
	 * 隐藏loading
	 */
	hideLoading() {
		if (this.isLoading) {
			this.isLoading = false
			uni.hideLoading()
		}
	}
	
	/**
	 * 通用请求方法
	 */
	request(options = {}) {
		// 合并配置
		const config = {
			...this.config,
			...options,
			header: {
				...this.config.header,
				...options.header
			}
		}
		
		// 处理URL
		if (!config.url.startsWith('http')) {
			config.url = config.baseURL + config.url
		}
		
		// 请求拦截
		const interceptedConfig = this.requestInterceptor(config)
		
		return new Promise((resolve, reject) => {
			uni.request({
				...interceptedConfig,
				success: (response) => {
					try {
						const result = this.responseInterceptor(response, config)
						resolve(result)
					} catch (error) {
						reject(error)
					}
				},
				fail: (error) => {
					const result = this.errorInterceptor(error, config)
					reject(result)
				}
			})
		})
	}
	
	/**
	 * GET请求
	 */
	get(url, params = {}, options = {}) {
		return this.request({
			url,
			method: 'GET',
			data: params,
			...options
		})
	}
	
	/**
	 * POST请求
	 */
	post(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'POST',
			data,
			...options
		})
	}
	
	/**
	 * PUT请求
	 */
	put(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'PUT',
			data,
			...options
		})
	}
	
	/**
	 * DELETE请求
	 */
	delete(url, params = {}, options = {}) {
		return this.request({
			url,
			method: 'DELETE',
			data: params,
			...options
		})
	}
}

// 创建实例
const http = new Request()

export default http
